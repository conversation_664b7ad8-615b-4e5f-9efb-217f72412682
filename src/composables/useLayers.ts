import { ref, computed, reactive, nextTick, onMounted } from 'vue';
import { GeoLayer, GroupLayer, LayerType } from '../types';
import { GeoServerService } from '../GeoServerService';
import { MapManager } from '../map';
import { getGeoServerCapabilitiesUrl } from '../config';

export function useLayerManager(mapManager: MapManager | null) {
  // Reactive state
  const layers = ref<GeoLayer[]>([]);
  const groups = ref<GroupLayer[]>([]);
  const isLoading = ref(false);
  const error = ref('');

  const geoServerService = new GeoServerService();

  // Computed properties for derived state (automatically reactive)
  const rootGroups = computed(() => {
    return groups.value.filter(group => !group.parent);
  });

  const ungroupedLayers = computed(() => {
    return layers.value.filter(layer => !layer.groupPath);
  });

  const fetchLayers = async () => {
    if (!mapManager) return;

    isLoading.value = true;
    error.value = '';

    try {
      console.log('Starting to fetch layers from GeoServer...');
      console.log('GeoServer URL:', getGeoServerCapabilitiesUrl());

      const fetchedLayers = await geoServerService.fetchLayers();
      console.log('Fetched layers count:', fetchedLayers.length);
      console.log('Fetched layers:', fetchedLayers);

      if (fetchedLayers.length > 0) {
        organizeLayersIntoGroups(fetchedLayers);
        fetchedLayers.forEach(layer => {
          layers.value.push(layer);
          mapManager.addLayer(layer);
        });

        console.log('Updated reactive layers:', layers.value);
        console.log('Updated root groups:', rootGroups.value);
        console.log('Updated ungrouped layers:', ungroupedLayers.value);
      } else {
        console.warn('No layers found in GeoServer');
        // Add test layers for debugging
        addTestLayers();
      }
    } catch (err) {
      console.error('Error fetching layers:', err);
      error.value = 'Error cargando capas desde GeoServer.';
      // Add test layers on error for debugging
      addTestLayers();
    } finally {
      isLoading.value = false;
    }
  };

  const addTestLayers = () => {
    const testLayers: GeoLayer[] = [
      {
        id: 'test_layer_1',
        name: 'Rivers',
        originalName: 'Hydrology/Rivers',
        groupPath: 'Hydrology',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 10,
        legend_url: '',
        abstract: 'Rivers in the study area'
      },
      {
        id: 'test_layer_2',
        name: 'Lakes',
        originalName: 'Hydrology/Lakes',
        groupPath: 'Hydrology',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 11,
        legend_url: '',
        abstract: 'Lakes in the study area'
      },
      {
        id: 'test_layer_3',
        name: 'Roads',
        originalName: 'Infrastructure/Roads',
        groupPath: 'Infrastructure',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 12,
        legend_url: '',
        abstract: 'Road network'
      },
      {
        id: 'test_layer_4',
        name: 'Buildings',
        originalName: 'Infrastructure/Buildings',
        groupPath: 'Infrastructure',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 13,
        legend_url: '',
        abstract: 'Building footprints'
      },
      {
        id: 'test_layer_5',
        name: 'Elevation',
        originalName: 'Elevation',
        type: LayerType.WMS,
        visible: false,
        opacity: 0.6,
        zIndex: 14,
        legend_url: '',
        abstract: 'Digital elevation model'
      }
    ];

    organizeLayersIntoGroups(testLayers);
    testLayers.forEach(layer => {
      layers.value.push(layer);
    });

    console.log('Added test layers:', layers.value);
    console.log('Test root groups:', rootGroups.value);
    console.log('Test ungrouped layers:', ungroupedLayers.value);
  };

  const organizeLayersIntoGroups = (fetchedLayers: GeoLayer[]): void => {
    groups.value = [];
    const groupMap = new Map<string, GroupLayer>();

    fetchedLayers.forEach(layer => {
      if (!layer.groupPath) return;

      const groupNames = layer.groupPath.split('/');
      let currentPath = '';
      let parentGroup: GroupLayer | undefined;
      let level = 0;

      groupNames.forEach((groupName: string) => {
        currentPath = currentPath ? `${currentPath}/${groupName}` : groupName;
        let group = groupMap.get(currentPath);

        if (!group) {
          group = {
            id: `group_${currentPath.replace(/\//g, '_')}`,
            name: groupName,
            children: [] as (GeoLayer | GroupLayer)[],
            collapsed: false, // Start expanded for better UX
            visible: false,
            level,
            parent: parentGroup,
          } as GroupLayer;
          groups.value.push(group);
          groupMap.set(currentPath, group);
          if (parentGroup) {
            parentGroup.children.push(group);
          }
        }
        parentGroup = group;
        level++;
      });

      layer.parent = parentGroup;
      if (parentGroup) {
        parentGroup.children.push(layer);
      }
    });
  };

  const toggleGroupCollapse = (groupId: string): void => {
    console.log('Toggling group collapse for:', groupId);
    const group = groups.value.find(g => g.id === groupId);
    if (group) {
      console.log('Group found, current collapsed state:', group.collapsed);
      group.collapsed = !group.collapsed;
      console.log('New collapsed state:', group.collapsed);
    } else {
      console.log('Group not found:', groupId);
    }
  };

  const toggleGroupVisibility = (groupId: string): void => {
    const group = groups.value.find(g => g.id === groupId);
    if (!group) return;

    const newVisibility = !group.visible;
    console.log(`Toggling group ${group.name} visibility from ${group.visible} to ${newVisibility}`);

    group.visible = newVisibility;
    updateChildrenVisibility(group, newVisibility);

    console.log(`Group ${group.name} visibility updated.`);
  };

  const updateChildrenVisibility = (group: GroupLayer, visible: boolean): void => {
    group.children.forEach((child: GeoLayer | GroupLayer) => {
      if ('children' in child) {
        const childGroup = child as GroupLayer;
        childGroup.visible = visible;
        updateChildrenVisibility(childGroup, visible);
      } else {
        const layer = child as GeoLayer;
        // Update layer visibility directly without triggering parent updates to avoid recursion
        layer.visible = visible;
        if (mapManager) {
          mapManager.toggleLayerVisibility(layer.id, visible);
        }
      }
    });
  };

  const toggleLayerVisibility = (layerId: string, visible?: boolean): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapManager) return;

    const newVisibility = visible !== undefined ? visible : !layer.visible;
    console.log(`Toggling layer ${layer.name} visibility from ${layer.visible} to ${newVisibility}`);

    layer.visible = newVisibility;
    mapManager.toggleLayerVisibility(layerId, newVisibility);

    // Update parent group states based on children visibility
    updateParentGroupStates(layer);

    console.log(`Layer ${layer.name} visibility updated.`);
  };

  const updateParentGroupStates = (layer: GeoLayer): void => {
    if (!layer.parent) return;

    const parent = layer.parent;
    const childLayers = parent.children.filter(child => !('children' in child)) as GeoLayer[];
    const childGroups = parent.children.filter(child => 'children' in child) as GroupLayer[];

    // Check if all children (layers and groups) are visible
    const allChildLayersVisible = childLayers.every(child => child.visible);
    const allChildGroupsVisible = childGroups.every(child => child.visible);
    const allChildrenVisible = allChildLayersVisible && allChildGroupsVisible;

    // Check if any children (layers and groups) are visible
    const anyChildLayersVisible = childLayers.some(child => child.visible);
    const anyChildGroupsVisible = childGroups.some(child => child.visible);
    const anyChildrenVisible = anyChildLayersVisible || anyChildGroupsVisible;

    const oldParentVisibility = parent.visible;

    // Update parent visibility based on children
    if (allChildrenVisible) {
      parent.visible = true;
    } else if (!anyChildrenVisible) {
      parent.visible = false;
    }
    // For partial visibility, we keep the current state or could implement indeterminate logic

    if (oldParentVisibility !== parent.visible) {
      console.log(`Updated parent group ${parent.name} visibility from ${oldParentVisibility} to ${parent.visible}`);
    }

    // Recursively update grandparent states
    updateParentGroupStates(parent as any);
  };

  const updateLayerOpacity = (layerId: string, opacity: number): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapManager) return;

    layer.opacity = opacity;
    mapManager.updateLayerOpacity(layerId, opacity);
  };

  // Initialize layers when mapManager is available
  const initialize = async () => {
    if (mapManager) {
      await fetchLayers();
    }
  };

  return {
    // Reactive state
    layers,
    groups,
    rootGroups,
    ungroupedLayers,
    isLoading,
    error,

    // Methods
    initialize,
    fetchLayers,
    toggleGroupCollapse,
    toggleGroupVisibility,
    toggleLayerVisibility,
    updateLayerOpacity,

    // Utility methods
    getLayers: () => layers.value,
    getRootGroups: () => rootGroups.value,
    getUngroupedLayers: () => ungroupedLayers.value
  };
}
