<template>
  <div 
    v-if="visible" 
    ref="panelRef"
    class="legend-panel"
    :class="{ dragging: isDragging }"
  >
    <div class="legend-header">
      <div
        class="drag-handle"
        @mousedown="startDrag"
        @touchstart="startDragTouch"
        title="Drag to move panel"
      >
        <i class="fas fa-grip-vertical"></i>
      </div>
      <h2>Leyenda</h2>
      <button
        class="close-button"
        @click="$emit('close')"
        :aria-label="'Close legend panel'"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="legend-content">
      <div v-if="visibleLayers.length === 0" class="no-legend">
        No hay capas visibles para mostrar en la leyenda.
      </div>
      <div v-else class="legend-items">
        <div
          v-for="layer in visibleLayers"
          :key="layer.id"
          class="legend-item"
        >
          <h3 class="legend-layer-name">{{ layer.name }}</h3>
          <div class="legend-image-container">
            <div class="legend-image-wrapper">
              <!-- Loading state -->
              <div v-if="layer.legend_url && layer.legendState.loading" class="legend-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading legend...</span>
              </div>

              <!-- Error state -->
              <div v-else-if="layer.legend_url && layer.legendState.error" class="legend-placeholder">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Legend not available</span>
              </div>

              <!-- No legend URL -->
              <div v-else-if="!layer.legend_url" class="legend-placeholder">
                <i class="fas fa-image"></i>
                <span>No legend available</span>
              </div>

              <!-- Successful image load -->
              <img
                v-else
                :src="layer.legend_url"
                :alt="`Legend for ${layer.name}`"
                class="legend-image"
                :class="{ 'loaded': layer.legendState.loaded }"
                @error="onImageError(layer.id)"
                @load="onImageLoad(layer.id)"
                @loadstart="onImageLoadStart(layer.id)"
              />
            </div>

            <!-- Opacity controls -->
            <div class="legend-controls">
              <div class="opacity-slider-container">
                <i class="fas fa-adjust opacity-slider-icon"></i>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  :value="layer.opacity"
                  @input="updateOpacity(layer.id, $event)"
                  class="opacity-slider"
                  :aria-label="`Opacity slider for ${layer.name}`"
                />
                <i class="fas fa-adjust opacity-slider-icon faded"></i>
              </div>

              <div class="opacity-input-container">
                <span>Opacity:</span>
                <input
                  type="number"
                  min="0"
                  max="100"
                  :value="Math.round(layer.opacity * 100)"
                  @input="updateOpacityFromInput(layer.id, $event)"
                  class="opacity-input"
                  :aria-label="`Opacity input for ${layer.name}`"
                />
                <span>%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, reactive, type Ref } from 'vue';
import { useDraggable } from '../composables/useDraggable';
import type { GeoLayer } from '../types';
import type { LayerManager } from '../layers';

// Props and emits
const props = defineProps<{
  visible: boolean;
}>();

defineEmits<{
  close: [];
}>();

// Template refs
const panelRef = ref<HTMLElement>();

// Get services from injection
const getLayerManager = inject<() => LayerManager | null>('layerManager');
const layers = inject<Ref<GeoLayer[]>>('layers');

// Use composables
const { isDragging, startDrag, startDragTouch } = useDraggable(panelRef);

// Reactive state for legend image loading and errors
const legendImageStates = reactive<Record<string, {
  loaded: boolean;
  error: boolean;
  loading: boolean;
}>>({});

// Computed property for visible layers with enhanced legend state
const visibleLayers = computed(() => {
  if (!layers?.value) return [];
  return layers.value.filter(layer => layer.visible).map(layer => ({
    ...layer,
    legendState: legendImageStates[layer.id] || { loaded: false, error: false, loading: false }
  }));
});

// Initialize legend state for a layer
const initializeLegendState = (layerId: string) => {
  if (!legendImageStates[layerId]) {
    legendImageStates[layerId] = {
      loaded: false,
      error: false,
      loading: true
    };
  }
};

// Update opacity handlers
const updateOpacity = (layerId: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const opacity = parseFloat(target.value);
  const layerManager = getLayerManager?.();
  if (layerManager) {
    layerManager.updateLayerOpacity(layerId, opacity);
  }
};

const updateOpacityFromInput = (layerId: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const opacity = parseInt(target.value) / 100;
  const layerManager = getLayerManager?.();
  if (layerManager) {
    layerManager.updateLayerOpacity(layerId, opacity);
  }
};

// Reactive image event handlers
const onImageError = (layerId: string) => {
  legendImageStates[layerId] = {
    loaded: false,
    error: true,
    loading: false
  };
};

const onImageLoad = (layerId: string) => {
  legendImageStates[layerId] = {
    loaded: true,
    error: false,
    loading: false
  };
};

const onImageLoadStart = (layerId: string) => {
  initializeLegendState(layerId);
};
</script>

<style scoped>
.legend-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 300px;
  max-height: calc(100vh - 200px);
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.legend-panel.dragging {
  user-select: none;
  cursor: move;
}

.legend-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background-color: #0f5397;
  color: white;
  border-radius: 4px 4px 0 0;
  gap: 6px;
}

.drag-handle {
  cursor: move;
  color: rgba(255, 255, 255, 0.7);
  padding: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.drag-handle:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.legend-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.close-button {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  color: white;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.close-button:focus {
  outline: 2px solid #ffffff;
  outline-offset: 1px;
}

.legend-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.no-legend {
  text-align: center;
  padding: 16px;
  color: #6c757d;
  font-size: 13px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 8px;
}

.legend-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.legend-layer-name {
  margin: 0 0 6px 0;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.legend-image-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
}

.legend-image-wrapper {
  flex-shrink: 0;
}

.legend-image {
  max-width: 120px;
  max-height: 150px;
  height: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
  object-fit: contain;
}

.legend-image.loaded {
  opacity: 1;
}

.legend-controls {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.opacity-slider-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.opacity-slider-icon {
  color: #6c757d;
  font-size: 14px;
}

.opacity-slider-icon.faded {
  opacity: 0.3;
}

.opacity-slider {
  flex: 1;
  height: 5px;
  -webkit-appearance: none;
  appearance: none;
  background: #dee2e6;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.opacity-input-container {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #6c757d;
}

.opacity-input {
  width: 60px;
  height: 24px;
  padding: 2px 5px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.opacity-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.legend-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  color: #6c757d;
  font-size: 12px;
  text-align: center;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
  min-height: 60px;
  max-width: 120px;
}

.legend-placeholder i {
  font-size: 24px;
  opacity: 0.5;
}

.legend-placeholder .fa-spinner {
  color: #0f5397;
  opacity: 1;
  animation: spin 1s linear infinite;
}

.legend-placeholder .fa-exclamation-triangle {
  color: #dc3545;
  opacity: 1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .legend-panel {
    width: 260px;
    right: 8px;
    top: 8px;
    max-height: calc(100vh - 150px);
  }
}

@media (max-width: 480px) {
  .legend-panel {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
    top: 5px;
    max-height: calc(100vh - 100px);
  }
  
  .legend-content {
    padding: 8px;
  }
  
  .legend-layer-name {
    font-size: 13px;
  }
}
</style>
