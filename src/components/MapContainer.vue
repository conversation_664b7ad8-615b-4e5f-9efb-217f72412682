<template>
  <div id="map-container">
    <div id="map" ref="mapElement"></div>
    <FloatingMenu 
      @toggle-layers="toggleLayersPanel"
      @toggle-legend="toggleLegendPanel"
      @toggle-search="toggleSearchPanel"
      @zoom-home="zoomToHome"
    />
    <SearchPanel 
      v-model:visible="searchPanelVisible"
      @close="searchPanelVisible = false"
    />
    <LayersPanel 
      v-model:visible="layersPanelVisible"
      @close="layersPanelVisible = false"
    />
    <LegendPanel 
      v-model:visible="legendPanelVisible"
      @close="legendPanelVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide } from 'vue';
import { MapManager } from '../map';
import { useLayerManager } from '../composables/useLayers';
import FloatingMenu from './FloatingMenu.vue';
import SearchPanel from './SearchPanel.vue';
import LayersPanel from './LayersPanel.vue';
import LegendPanel from './LegendPanel.vue';

// Reactive state for panel visibility
const searchPanelVisible = ref(false);
const layersPanelVisible = ref(false);
const legendPanelVisible = ref(false);

// Map instance
const mapElement = ref<HTMLElement>();
let mapManager: MapManager | null = null;

// Layer manager will be initialized after mapManager is created
let layerManager: any = null;

// Panel toggle functions
const toggleSearchPanel = () => {
  searchPanelVisible.value = !searchPanelVisible.value;
};

const toggleLayersPanel = () => {
  layersPanelVisible.value = !layersPanelVisible.value;
};

const toggleLegendPanel = () => {
  legendPanelVisible.value = !legendPanelVisible.value;
};

const zoomToHome = () => {
  if (mapManager) {
    mapManager.zoomToHome();
  }
};

// Provide services and data to child components
provide('mapManager', () => mapManager);
provide('layerManager', () => layerManager);

onMounted(async () => {
  if (mapElement.value) {
    // Initialize map manager
    mapManager = new MapManager('map');

    // Initialize layer manager with the mapManager instance
    layerManager = useLayerManager(mapManager);

    // Initialize layers
    await layerManager.initialize();

    // Make mapManager globally available for debugging
    (window as any).mapManager = mapManager;
    (window as any).layerManager = layerManager;
  }
});

onUnmounted(() => {
  // Cleanup services
  if (mapManager) {
    mapManager.destroy();
  }
});
</script>

<style scoped>
#map-container {
  flex: 1;
  position: relative;
  height: 100%;
  min-height: 400px;
}

#map {
  width: 100%;
  height: 100%;
}

@media (max-width: 768px) {
  #map-container {
    min-height: 300px;
  }
}
</style>
